version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: massimo_erp
      POSTGRES_USER: massimo_user
      POSTGRES_PASSWORD: massimo_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U massimo_user -d massimo_erp"]
      interval: 10s
      timeout: 5s
      retries: 5

  # SpiceDB
  spicedb:
    image: "authzed/spicedb:latest"
    command: "serve"
    restart: "always"
    ports:
      - "8080:8080"   # HTTP API
      - "9090:9090"   # Metrics
      - "50051:50051" # gRPC API
    environment:
      - "SPICEDB_GRPC_PRESHARED_KEY=massimo-spicedb-preshared-key-2024"
      - "SPICEDB_DATASTORE_ENGINE=postgres"
      - "SPICEDB_DATASTORE_CONN_URI=******************************************************/massimo_erp?sslmode=disable"
      - "SPICEDB_LOG_LEVEL=debug"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "grpc_health_probe", "-addr=localhost:50051"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
